
import React from 'react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
    children: React.ReactNode;
    className?: string;
}

const Select: React.FC<SelectProps> = ({ children, className = '', ...props }) => {
    return (
        <div className={`relative ${className}`}>
            <select
                {...props}
                className="w-full appearance-none bg-slate-900/80 border border-slate-600/50 rounded-xl py-3 px-4 focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500/50 focus:outline-none transition-all duration-300 text-slate-200 disabled:bg-slate-700/50 disabled:cursor-not-allowed backdrop-blur-sm hover:border-slate-500/70"
            >
                {children}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-slate-400">
                <svg className="fill-current h-4 w-4 transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
            </div>
        </div>
    );
};

export default Select;
