
import React from 'react';
import type { AspectRatio } from '../types';
import Button from './Button';
import Select from './Select';

interface ControlPanelProps {
    prompt: string;
    setPrompt: (prompt: string) => void;
    numberOfImages: number;
    setNumberOfImages: (count: number) => void;
    aspectRatio: AspectRatio;
    setAspectRatio: (ratio: AspectRatio) => void;
    imageModels: string[];
    selectedModel: string;
    aspectRatios: { value: AspectRatio, label: string }[];
    imageCounts: number[];
    onGenerate: () => void;
    isLoading: boolean;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
    prompt,
    setPrompt,
    numberOfImages,
    setNumberOfImages,
    aspectRatio,
    setAspectRatio,
    imageModels,
    selectedModel,
    aspectRatios,
    imageCounts,
    onGenerate,
    isLoading
}) => {
    return (
        <div className="space-y-6">
            {/* Prompt Section */}
            <div className="space-y-3">
                <label className="text-sm font-semibold text-slate-300 flex items-center gap-2">
                    <svg className="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Describe Your Vision
                </label>
                <div className="relative">
                    <textarea
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        placeholder="Describe the image you want to create... Be as detailed and creative as you like!"
                        className="w-full bg-slate-900/80 border border-slate-600/50 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500/50 focus:outline-none transition-all duration-300 resize-none min-h-[120px] text-slate-200 placeholder-slate-500 backdrop-blur-sm"
                        rows={5}
                    />
                    <div className="absolute bottom-3 right-3 text-xs text-slate-500">
                        {prompt.length} characters
                    </div>
                </div>
            </div>

            {/* Settings Grid */}
            <div className="space-y-4">
                <h3 className="text-sm font-semibold text-slate-300 flex items-center gap-2">
                    <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Generation Settings
                </h3>

                <div className="space-y-4">
                    {/* Model Selection */}
                    <div className="space-y-2">
                        <label htmlFor="model-select" className="text-xs font-medium text-slate-400 uppercase tracking-wide">AI Model</label>
                        <div className="relative">
                            <Select
                                id="model-select"
                                value={selectedModel}
                                onChange={() => {}} // Model is not changeable for now
                                disabled={true}
                                className="w-full"
                            >
                                {imageModels.map(model => <option key={model} value={model}>{model}</option>)}
                            </Select>
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs bg-indigo-500/20 text-indigo-300 px-2 py-1 rounded-full">
                                Pro
                            </div>
                        </div>
                    </div>

                    {/* Aspect Ratio with Preview */}
                    <div className="space-y-3">
                        <label htmlFor="aspect-ratio-select" className="text-xs font-medium text-slate-400 uppercase tracking-wide">Aspect Ratio</label>

                        {/* Aspect Ratio Preview */}
                        <div className="flex items-center gap-3 mb-2">
                            <div className="flex-1">
                                <Select
                                    id="aspect-ratio-select"
                                    value={aspectRatio}
                                    onChange={(e) => setAspectRatio(e.target.value as AspectRatio)}
                                    disabled={isLoading}
                                    className="w-full"
                                >
                                    {aspectRatios.map(ratio => <option key={ratio.value} value={ratio.value}>{ratio.label}</option>)}
                                </Select>
                            </div>

                            {/* Live Preview */}
                            <div className="flex-shrink-0">
                                <div className="relative">
                                    <div className={`w-12 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 border border-indigo-500/30 rounded-lg transition-all duration-300 ${
                                        aspectRatio === '16:9' ? 'h-7' :
                                        aspectRatio === '9:16' ? 'h-16' :
                                        aspectRatio === '4:3' ? 'h-9' :
                                        aspectRatio === '3:4' ? 'h-16' :
                                        'h-12' // 1:1
                                    }`}>
                                        <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/10 to-purple-400/10 rounded-lg animate-pulse"></div>
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            <div className="text-[8px] font-bold text-indigo-300">{aspectRatio}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Number of Images */}
                    <div className="space-y-3">
                        <label className="text-xs font-medium text-slate-400 uppercase tracking-wide">Number of Images</label>
                        <div className="grid grid-cols-4 gap-2 bg-slate-900/50 p-2 rounded-xl border border-slate-700/50">
                            {imageCounts.map(count => (
                                <button
                                    key={count}
                                    onClick={() => setNumberOfImages(count)}
                                    disabled={isLoading}
                                    className={`relative px-3 py-2.5 rounded-lg text-sm font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-indigo-500 ${
                                        numberOfImages === count
                                        ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg transform scale-105'
                                        : 'bg-slate-800/50 text-slate-300 hover:bg-slate-700/50 hover:text-white'
                                    }`}
                                >
                                    {count}
                                    {numberOfImages === count && (
                                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
                                    )}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Output Preview */}
            <div className="space-y-3">
                <h3 className="text-sm font-semibold text-slate-300 flex items-center gap-2">
                    <svg className="w-4 h-4 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    Output Preview
                </h3>

                {/* Preview Grid */}
                <div className="bg-slate-900/50 border border-slate-700/30 rounded-xl p-4">
                    <div className="text-xs text-slate-400 mb-3 text-center">
                        {numberOfImages} image{numberOfImages !== 1 ? 's' : ''} • {aspectRatio}
                    </div>

                    <div className={`grid gap-2 ${
                        numberOfImages === 1 ? 'grid-cols-1' :
                        numberOfImages === 2 ? 'grid-cols-2' :
                        numberOfImages === 3 ? 'grid-cols-3' :
                        'grid-cols-2'
                    }`}>
                        {Array.from({ length: numberOfImages }).map((_, index) => (
                            <div
                                key={index}
                                className={`bg-gradient-to-br from-slate-700/30 to-slate-800/30 border border-slate-600/30 rounded-lg flex items-center justify-center transition-all duration-300 hover:border-indigo-500/50 ${
                                    aspectRatio === '16:9' ? 'aspect-[16/9]' :
                                    aspectRatio === '9:16' ? 'aspect-[9/16]' :
                                    aspectRatio === '4:3' ? 'aspect-[4/3]' :
                                    aspectRatio === '3:4' ? 'aspect-[3/4]' :
                                    'aspect-square'
                                }`}
                            >
                                <div className="text-center space-y-1">
                                    <div className="w-4 h-4 mx-auto bg-slate-600/50 rounded-full flex items-center justify-center">
                                        <div className="w-2 h-2 bg-indigo-400/60 rounded-full animate-pulse"></div>
                                    </div>
                                    <div className="text-[10px] text-slate-500 font-medium">#{index + 1}</div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Generate Button */}
            <div className="pt-4">
                <Button
                    onClick={onGenerate}
                    disabled={isLoading}
                    className="w-full relative overflow-hidden group"
                >
                    <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative flex items-center justify-center gap-3">
                        {isLoading ? (
                            <>
                                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                <span>Creating Magic...</span>
                            </>
                        ) : (
                            <>
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                <span>Generate Images</span>
                            </>
                        )}
                    </div>
                </Button>
            </div>
        </div>
    );
}

export default ControlPanel;