
import React from 'react';
import type { AspectRatio } from '../types';

interface ImageSkeletonProps {
    aspectRatio: AspectRatio;
}

const getAspectRatioClass = (ratio: AspectRatio): string => {
    switch (ratio) {
        case '16:9': return 'aspect-[16/9]';
        case '9:16': return 'aspect-[9/16]';
        case '4:3': return 'aspect-[4/3]';
        case '3:4': return 'aspect-[3/4]';
        case '1:1':
        default:
            return 'aspect-square';
    }
}

const ImageSkeleton: React.FC<ImageSkeletonProps> = ({ aspectRatio }) => {
    const aspectRatioClass = getAspectRatioClass(aspectRatio);

    return (
        <div className={`relative w-full bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 rounded-2xl overflow-hidden backdrop-blur-sm group ${aspectRatioClass}`}>
            {/* Animated Background Layers */}
            <div className="absolute inset-0 bg-gradient-to-r from-slate-800/50 via-slate-700/30 to-slate-800/50 animate-pulse"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/10 via-purple-900/10 to-pink-900/10 animate-pulse [animation-delay:0.5s]"></div>

            {/* Multiple Shimmer Effects */}
            <div className="absolute inset-0 -translate-x-full animate-[shimmer_3s_infinite] bg-gradient-to-r from-transparent via-white/8 to-transparent"></div>
            <div className="absolute inset-0 -translate-x-full animate-[shimmer_3s_infinite] bg-gradient-to-r from-transparent via-indigo-400/10 to-transparent [animation-delay:1s]"></div>

            {/* Floating Particles */}
            <div className="absolute top-4 left-4 w-1 h-1 bg-indigo-400/40 rounded-full animate-ping [animation-delay:0.5s]"></div>
            <div className="absolute top-8 right-6 w-1 h-1 bg-purple-400/40 rounded-full animate-ping [animation-delay:1.2s]"></div>
            <div className="absolute bottom-6 left-8 w-1 h-1 bg-pink-400/40 rounded-full animate-ping [animation-delay:2s]"></div>

            {/* Central Content */}
            <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-6">
                    {/* Enhanced AI Brain Icon */}
                    <div className="relative w-16 h-16 mx-auto">
                        {/* Rotating Outer Ring */}
                        <div className="absolute inset-0 border-2 border-indigo-500/30 rounded-full animate-spin [animation-duration:4s]"></div>
                        <div className="absolute inset-1 border-2 border-purple-500/20 rounded-full animate-spin [animation-duration:3s] [animation-direction:reverse]"></div>

                        {/* Central Icon */}
                        <div className="absolute inset-0 bg-gradient-to-br from-slate-700/80 to-slate-800/80 rounded-full flex items-center justify-center backdrop-blur-sm">
                            <svg className="w-8 h-8 text-indigo-400 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423L16.5 15.75l.394 1.183a2.25 2.25 0 001.423 1.423L19.5 18.75l-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                            </svg>
                        </div>

                        {/* Pulsing Glow */}
                        <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-pink-500/20 rounded-full animate-pulse blur-sm"></div>
                    </div>

                    {/* Enhanced Loading Dots */}
                    <div className="flex items-center justify-center gap-2">
                        <div className="w-2.5 h-2.5 bg-indigo-500/80 rounded-full animate-bounce [animation-delay:-0.4s]"></div>
                        <div className="w-2.5 h-2.5 bg-purple-500/80 rounded-full animate-bounce [animation-delay:-0.2s]"></div>
                        <div className="w-2.5 h-2.5 bg-pink-500/80 rounded-full animate-bounce"></div>
                        <div className="w-2.5 h-2.5 bg-emerald-500/80 rounded-full animate-bounce [animation-delay:0.2s]"></div>
                    </div>

                    {/* Progress Indicator */}
                    <div className="w-24 h-1 bg-slate-700/50 rounded-full mx-auto overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>

            {/* Animated Border */}
            <div className="absolute inset-0 rounded-2xl">
                <div className="absolute inset-0 rounded-2xl border border-transparent bg-gradient-to-r from-indigo-500/30 via-purple-500/30 to-pink-500/30 animate-pulse"></div>
                <div className="absolute inset-0 rounded-2xl border border-transparent bg-gradient-to-l from-emerald-500/20 via-teal-500/20 to-cyan-500/20 animate-pulse [animation-delay:1s]"></div>
            </div>

            {/* Corner Accents */}
            <div className="absolute top-2 left-2 w-3 h-3 border-l-2 border-t-2 border-indigo-400/50 rounded-tl-lg animate-pulse"></div>
            <div className="absolute top-2 right-2 w-3 h-3 border-r-2 border-t-2 border-purple-400/50 rounded-tr-lg animate-pulse [animation-delay:0.5s]"></div>
            <div className="absolute bottom-2 left-2 w-3 h-3 border-l-2 border-b-2 border-pink-400/50 rounded-bl-lg animate-pulse [animation-delay:1s]"></div>
            <div className="absolute bottom-2 right-2 w-3 h-3 border-r-2 border-b-2 border-emerald-400/50 rounded-br-lg animate-pulse [animation-delay:1.5s]"></div>
        </div>
    );
};

export default ImageSkeleton;