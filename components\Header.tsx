
import React from 'react';

const Header: React.FC = () => {
    const Logo = () => (
        <div className="relative">
            <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-400"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-500"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-600"/>
            </svg>
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full opacity-20 blur-xl animate-pulse"></div>
        </div>
    );

    return (
        <header className="bg-slate-900/80 backdrop-blur-xl sticky top-0 z-50 border-b border-slate-700/30 shadow-lg">
            <div className="container mx-auto px-6">
                <div className="flex items-center justify-between h-20">
                    <div className="flex items-center space-x-4 group cursor-pointer">
                        <div className="transition-all duration-500 group-hover:scale-110 group-hover:rotate-12">
                            <Logo />
                        </div>
                        <div className="space-y-1">
                            <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent transition-all duration-300 group-hover:from-indigo-300 group-hover:via-purple-300 group-hover:to-pink-300">
                                Imagen Studio
                            </h1>
                            <p className="text-xs text-slate-400 font-medium tracking-wide opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                AI-Powered Creative Suite
                            </p>
                        </div>
                    </div>

                    {/* Status Indicator */}
                    <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2 bg-slate-800/50 backdrop-blur-sm px-3 py-2 rounded-full border border-slate-700/50">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                            <span className="text-xs text-slate-300 font-medium">Online</span>
                        </div>

                        {/* Version Badge */}
                        <div className="hidden sm:flex items-center gap-2 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm px-3 py-2 rounded-full border border-indigo-500/30">
                            <svg className="w-3 h-3 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <span className="text-xs text-indigo-300 font-medium">v2.0</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;