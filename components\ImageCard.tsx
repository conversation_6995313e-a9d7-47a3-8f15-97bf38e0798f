
import React, { useState, useEffect } from 'react';
import type { AspectRatio } from '../types';

interface ImageCardProps {
  src: string;
  prompt: string;
  aspectRatio: AspectRatio;
}

const DownloadIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
    </svg>
);

const getAspectRatioClass = (ratio: AspectRatio): string => {
    switch (ratio) {
        case '16:9': return 'aspect-[16/9]';
        case '9:16': return 'aspect-[9/16]';
        case '4:3': return 'aspect-[4/3]';
        case '3:4': return 'aspect-[3/4]';
        case '1:1':
        default:
            return 'aspect-square';
    }
}

const ImageCard: React.FC<ImageCardProps> = ({ src, prompt, aspectRatio }) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const aspectRatioClass = getAspectRatioClass(aspectRatio);

    useEffect(() => {
        const image = new Image();
        image.src = src;
        image.onload = () => {
            setIsLoaded(true);
        };
    }, [src]);

    const handleDownload = () => {
        const link = document.createElement('a');
        link.href = src;
        // Sanitize prompt for filename
        const filename = prompt.substring(0, 30).replace(/[^a-z0-9]/gi, '_').toLowerCase();
        link.download = `imagen-studio-${filename}-${Date.now()}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(prompt);
            // You could add a toast notification here
        } catch (err) {
            console.error('Failed to copy prompt:', err);
        }
    };

    return (
        <div
            className={`group relative overflow-hidden rounded-2xl shadow-2xl bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 backdrop-blur-sm transition-all duration-500 ease-out hover:shadow-indigo-500/20 hover:border-indigo-500/30 hover:scale-[1.02] ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >
            {/* Image */}
            <div className={`relative overflow-hidden ${aspectRatioClass}`}>
                <img
                    src={src}
                    alt={prompt}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

                {/* Loading State */}
                {!isLoaded && (
                    <div className="absolute inset-0 bg-slate-800 animate-pulse flex items-center justify-center">
                        <div className="w-8 h-8 border-2 border-indigo-500/30 border-t-indigo-500 rounded-full animate-spin"></div>
                    </div>
                )}
            </div>

            {/* Action Buttons - Top Right */}
            <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                <button
                    onClick={handleCopy}
                    className="bg-black/40 text-white backdrop-blur-md p-2.5 rounded-xl hover:bg-black/60 transition-all duration-300 hover:scale-110"
                    aria-label="Copy Prompt"
                    title="Copy Prompt"
                >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                </button>
                <button
                    onClick={handleDownload}
                    className="bg-gradient-to-r from-indigo-500/80 to-purple-500/80 text-white backdrop-blur-md p-2.5 rounded-xl hover:from-indigo-600 hover:to-purple-600 transition-all duration-300 hover:scale-110 shadow-lg"
                    aria-label="Download Image"
                    title="Download Image"
                >
                   <DownloadIcon />
                </button>
            </div>

            {/* Content Overlay - Bottom */}
            <div className="absolute bottom-0 left-0 right-0 p-6">
                <div className="space-y-3">
                    {/* Prompt Text */}
                    <div className={`transition-all duration-500 ${isHovered ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'}`}>
                        <div className="bg-black/40 backdrop-blur-md rounded-xl p-4 border border-white/10">
                            <p className="text-white text-sm font-medium leading-relaxed">
                                {prompt.length > 120 ? `${prompt.substring(0, 120)}...` : prompt}
                            </p>
                        </div>
                    </div>

                    {/* Status Indicator */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 text-white/80">
                            <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                            <span className="text-xs font-medium">Generated</span>
                        </div>
                        <div className="text-xs text-white/60">
                            {new Date().toLocaleTimeString()}
                        </div>
                    </div>
                </div>
            </div>

            {/* Shine Effect */}
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 translate-x-full group-hover:-translate-x-full transition-transform duration-1000"></div>
            </div>
        </div>
    );
};

export default ImageCard;