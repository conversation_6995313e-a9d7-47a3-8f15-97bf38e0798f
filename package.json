{"name": "imagen-studio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@google/genai": "^1.12.0", "@headlessui/react": "^2.2.7", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.535.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hotkeys-hook": "^5.1.0", "react-image-gallery": "^1.4.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react-image-gallery": "^1.2.4", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.2", "vite": "^6.2.0"}}