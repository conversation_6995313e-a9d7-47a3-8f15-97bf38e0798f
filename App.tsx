
import React from 'react';
import Header from './components/Header';
import ImageGenerator from './components/ImageGenerator';

const App: React.FC = () => {
  return (
    <div className="min-h-screen text-slate-200 bg-slate-900 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.3),rgba(255,255,255,0))]">
      <Header />
      <main className="container mx-auto px-6 py-12 max-w-7xl">
        <div className="space-y-8">
          {/* Hero Section */}
          <div className="text-center space-y-4 mb-12">
            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-gradient">
              Transform Ideas into Art
            </h2>
            <p className="text-xl text-slate-400 max-w-2xl mx-auto leading-relaxed">
              Harness the power of AI to create stunning, unique images from your imagination.
              Professional-quality results in seconds.
            </p>
          </div>

          <ImageGenerator />
        </div>
      </main>

      <footer className="border-t border-slate-800/50 bg-slate-900/50 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-pulse"></div>
              <p className="text-slate-400 text-sm">
                Powered by <span className="text-indigo-400 font-semibold">Google Gemini</span> &
                <span className="text-purple-400 font-semibold"> React</span>
              </p>
            </div>
            <div className="flex items-center gap-6 text-sm text-slate-500">
              <span className="hover:text-slate-300 transition-colors cursor-pointer">Privacy</span>
              <span className="hover:text-slate-300 transition-colors cursor-pointer">Terms</span>
              <span className="hover:text-slate-300 transition-colors cursor-pointer">Support</span>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-slate-800/30 text-center">
            <p className="text-xs text-slate-500">
              © 2024 Imagen Studio. Designed for modern creative workflows.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default App;